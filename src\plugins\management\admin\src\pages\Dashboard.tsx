import React, { useState, useEffect } from 'react';
import { Spin, Row, Col, Typography, Space, Table as AntTable, Empty } from 'antd';
import {
  ReloadOutlined,
  DollarOutlined,
  ShoppingCartOutlined,
  UserOutlined,
  AppstoreOutlined,
  RiseOutlined,
  CrownOutlined,
  FireOutlined,
} from '@ant-design/icons';
import { useFetchClient } from '@strapi/strapi/admin';
import {
  PageContainer,
  Card,
  CardContent,
  PageHeader,
  StatsGrid,
  StatsCard,
  Button,
  StyledTable,
} from '../components/shared';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area,
} from 'recharts';
import { TrendingUp, BarChart3, Users, Package, Award, ShoppingCart } from 'lucide-react';

const { Title } = Typography;

interface DashboardStats {
  totalRevenue: number;
  totalOrders: number;
  totalUsers: number;
  totalProducts: number;
}

interface ChartData {
  month: string;
  revenue: number;
  orders: number;
}

interface TopCustomer {
  id: string;
  name: string;
  email: string;
  totalOrders: number;
  totalSpent: number;
}

interface BestSellingProduct {
  id: string;
  name: string;
  sold: number;
  revenue: number;
}

interface RecentOrder {
  id: string;
  code: string;
  customerName: string;
  customerPhone: string;
  total: number;
  status: string;
  productCount: number;
  createdAt: string;
}

const Dashboard: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState<DashboardStats>({
    totalRevenue: 0,
    totalOrders: 0,
    totalUsers: 0,
    totalProducts: 0,
  });
  const [revenueData, setRevenueData] = useState<ChartData[]>([]);
  const [topCustomers, setTopCustomers] = useState<TopCustomer[]>([]);
  const [bestSellingProducts, setBestSellingProducts] = useState<BestSellingProduct[]>([]);
  const [recentOrders, setRecentOrders] = useState<RecentOrder[]>([]);

  const { get } = useFetchClient();

  // Fetch dashboard data
  const fetchDashboardData = async () => {
    setLoading(true);
    try {
      // Fetch KPI data
      const kpiResponse = await get('/management/dashboard/kpi');
      setStats(kpiResponse.data);

      // Fetch chart data
      const chartResponse = await get('/management/dashboard/charts/revenue');
      setRevenueData(chartResponse.data.data || []);

      // Fetch top customers
      const customersResponse = await get('/management/dashboard/top-customers');
      setTopCustomers(customersResponse.data.data || []);

      // Fetch best selling products
      const productsResponse = await get('/management/dashboard/best-selling-products');
      setBestSellingProducts(productsResponse.data.data || []);

      // Fetch recent orders
      const ordersResponse = await get('/management/orders', {
        params: { page: 1, pageSize: 5, sortBy: 'createdAt', sortOrder: 'desc' },
      });

      // Transform order data to match interface
      const transformedOrders = (ordersResponse.data.data || []).map((order: any) => ({
        id: order.id,
        code: order.code,
        customerName: order.customer?.name || 'N/A',
        customerPhone: order.customer?.phone || 'N/A',
        total: order.priceAfterTax,
        status: order.statusOrder,
        productCount: order.products?.length || 0,
        createdAt: order.createdAt,
      }));

      setRecentOrders(transformedOrders);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
  }, []);

  // Stats data for cards
  const statsData = [
    {
      title: 'Doanh thu',
      value: new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND',
      }).format(stats.totalRevenue),
      icon: DollarOutlined,
      color: 'bg-green',
    },
    {
      title: 'Tổng đơn hàng',
      value: stats.totalOrders.toLocaleString('vi-VN'),
      icon: ShoppingCartOutlined,
      color: 'bg-blue',
    },
    {
      title: 'Tổng đại lý',
      value: stats.totalUsers.toLocaleString('vi-VN'),
      icon: UserOutlined,
      color: 'bg-blue-dark',
    },
    {
      title: 'Tổng sản phẩm',
      value: stats.totalProducts.toLocaleString('vi-VN'),
      icon: AppstoreOutlined,
      color: 'bg-red',
    },
  ];

  // Top customers table columns
  const customerColumns = [
    {
      title: 'Tên khách hàng',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: 'Số đơn hàng',
      dataIndex: 'totalOrders',
      key: 'totalOrders',
      render: (value: number) => value.toLocaleString('vi-VN'),
    },
    {
      title: 'Tổng chi tiêu',
      dataIndex: 'totalSpent',
      key: 'totalSpent',
      render: (value: number) =>
        new Intl.NumberFormat('vi-VN', {
          style: 'currency',
          currency: 'VND',
        }).format(value),
    },
  ];

  // Best selling products table columns
  const productColumns = [
    {
      title: 'Tên sản phẩm',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Đã bán',
      dataIndex: 'sold',
      key: 'sold',
      render: (value: number) => value.toLocaleString('vi-VN'),
    },
    {
      title: 'Doanh thu',
      dataIndex: 'revenue',
      key: 'revenue',
      render: (value: number) =>
        new Intl.NumberFormat('vi-VN', {
          style: 'currency',
          currency: 'VND',
        }).format(value),
    },
  ];

  // Recent orders table columns
  const orderColumns = [
    {
      title: 'Mã đơn hàng',
      dataIndex: 'code',
      key: 'code',
      width: 120,
      render: (value: string) => <div style={{ fontWeight: 600, color: '#1e293b' }}>{value}</div>,
    },
    {
      title: 'Khách hàng',
      dataIndex: 'customerName',
      key: 'customerName',
      width: 150,
    },
    {
      title: 'Số điện thoại',
      dataIndex: 'customerPhone',
      key: 'customerPhone',
      width: 120,
    },
    {
      title: 'Tổng tiền',
      dataIndex: 'total',
      key: 'total',
      width: 120,
      render: (value: number) => (
        <div style={{ fontWeight: 600, color: '#000' }}>
          {new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND',
          }).format(value)}
        </div>
      ),
    },
    {
      title: 'Số sản phẩm',
      dataIndex: 'productCount',
      key: 'productCount',
      width: 100,
      render: (count: number) => (
        <div style={{ textAlign: 'center', fontWeight: 500 }}>{count || 0}</div>
      ),
    },
    {
      title: 'Trạng thái đơn hàng',
      dataIndex: 'status',
      key: 'status',
      width: 140,
      render: (status: string) => {
        const statusMap: { [key: string]: { text: string; color: string; bg: string } } = {
          'Chờ xác nhận': { text: 'Chờ xác nhận', color: '#f59e0b', bg: '#fffbeb' },
          'Chờ giao hàng': { text: 'Chờ giao hàng', color: '#3b82f6', bg: '#eff6ff' },
          'Đang giao hàng': { text: 'Đang giao hàng', color: '#8b5cf6', bg: '#f3f4f6' },
          'Đã hoàn thành': { text: 'Hoàn thành', color: '#10b981', bg: '#ecfdf5' },
          'Đã hủy': { text: 'Đã hủy', color: '#ef4444', bg: '#fef2f2' },
        };
        const statusInfo = statusMap[status] || { text: status, color: '#6b7280', bg: '#f9fafb' };
        return (
          <span
            style={{
              color: statusInfo.color,
              fontWeight: 500,
              padding: '4px 8px',
              borderRadius: '4px',
              backgroundColor: statusInfo.bg,
              fontSize: '12px',
            }}
          >
            {statusInfo.text}
          </span>
        );
      },
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 120,
      render: (date: string) => (
        <div style={{ fontSize: '13px', color: '#6b7280' }}>
          {new Date(date).toLocaleDateString('vi-VN', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
          })}
        </div>
      ),
    },
  ];

  return (
    <PageContainer>
      <Spin spinning={loading} tip="Đang tải dữ liệu...">
        {/* Stats Cards */}
        <StatsGrid>
          {statsData.map((stat, index) => (
            <StatsCard
              key={index}
              title={stat.title}
              value={stat.value}
              icon={<stat.icon />}
              color={stat.color}
            />
          ))}
        </StatsGrid>

        {/* Charts Section */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          {/* Revenue Chart */}
          <Col xs={24} lg={12}>
            <Card>
              <CardContent>
                <div style={{ marginBottom: 16 }}>
                  <Title
                    level={4}
                    style={{ margin: 0, fontFamily: "'Be Vietnam Pro', sans-serif" }}
                  >
                    <Space>
                      <RiseOutlined style={{ color: '#3b82f6' }} />
                      Thống kê doanh thu
                    </Space>
                  </Title>
                </div>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={revenueData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip
                      formatter={(value, name) => [
                        name === 'revenue'
                          ? new Intl.NumberFormat('vi-VN', {
                              style: 'currency',
                              currency: 'VND',
                            }).format(Number(value))
                          : value,
                        name === 'revenue' ? 'Doanh thu' : 'Đơn hàng',
                      ]}
                    />
                    <Area
                      type="monotone"
                      dataKey="revenue"
                      stroke="#3b82f6"
                      fill="#3b82f6"
                      fillOpacity={0.3}
                      name="Doanh thu"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Col>

          {/* Orders Chart */}
          <Col xs={24} lg={12}>
            <Card>
              <CardContent>
                <div style={{ marginBottom: 16 }}>
                  <Title
                    level={4}
                    style={{ margin: 0, fontFamily: "'Be Vietnam Pro', sans-serif" }}
                  >
                    <Space>
                      <ShoppingCartOutlined style={{ color: '#10b981' }} />
                      Đơn hàng
                    </Space>
                  </Title>
                </div>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={revenueData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="orders" fill="#10b981" name="Số đơn hàng" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Col>
        </Row>

        {/* Tables Section */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          {/* Best Selling Products */}
          <Col xs={24} lg={12}>
            <Card>
              <CardContent>
                <div style={{ marginBottom: 16 }}>
                  <Title
                    level={4}
                    style={{ margin: 0, fontFamily: "'Be Vietnam Pro', sans-serif" }}
                  >
                    <Space>
                      <FireOutlined style={{ color: '#ef4444' }} />
                      Sản phẩm bán chạy
                    </Space>
                  </Title>
                </div>
                {bestSellingProducts.length > 0 ? (
                  <StyledTable>
                    <AntTable
                      dataSource={bestSellingProducts}
                      columns={productColumns}
                      pagination={false}
                      size="small"
                      rowKey="id"
                      locale={{
                        emptyText: (
                          <Empty
                            image={Empty.PRESENTED_IMAGE_SIMPLE}
                            description={
                              <span
                                style={{
                                  color: '#64748b',
                                  fontFamily: "'Be Vietnam Pro', sans-serif",
                                }}
                              >
                                Không có dữ liệu
                              </span>
                            }
                          />
                        ),
                      }}
                      style={{
                        fontFamily: "'Be Vietnam Pro', sans-serif",
                      }}
                    />
                  </StyledTable>
                ) : (
                  <Empty description="Không có dữ liệu" />
                )}
              </CardContent>
            </Card>
          </Col>

          {/* Top Customers */}
          <Col xs={24} lg={12}>
            <Card>
              <CardContent>
                <div style={{ marginBottom: 16 }}>
                  <Title
                    level={4}
                    style={{ margin: 0, fontFamily: "'Be Vietnam Pro', sans-serif" }}
                  >
                    <Space>
                      <CrownOutlined style={{ color: '#f59e0b' }} />
                      Top khách hàng
                    </Space>
                  </Title>
                </div>
                {topCustomers.length > 0 ? (
                  <StyledTable>
                    <AntTable
                      dataSource={topCustomers}
                      columns={customerColumns}
                      pagination={false}
                      size="small"
                      rowKey="id"
                      locale={{
                        emptyText: (
                          <Empty
                            image={Empty.PRESENTED_IMAGE_SIMPLE}
                            description={
                              <span
                                style={{
                                  color: '#64748b',
                                  fontFamily: "'Be Vietnam Pro', sans-serif",
                                }}
                              >
                                Không có dữ liệu
                              </span>
                            }
                          />
                        ),
                      }}
                      style={{
                        fontFamily: "'Be Vietnam Pro', sans-serif",
                      }}
                    />
                  </StyledTable>
                ) : (
                  <Empty description="Không có dữ liệu" />
                )}
              </CardContent>
            </Card>
          </Col>
        </Row>

        {/* Recent Orders */}
        <Card>
          <CardContent>
            <div style={{ marginBottom: 16 }}>
              <Title level={4} style={{ margin: 0, fontFamily: "'Be Vietnam Pro', sans-serif" }}>
                <Space>
                  <ShoppingCartOutlined style={{ color: '#3b82f6' }} />
                  Danh sách đơn hàng gần đây
                </Space>
              </Title>
            </div>
            {recentOrders.length > 0 ? (
              <StyledTable>
                <AntTable
                  dataSource={recentOrders}
                  columns={orderColumns}
                  pagination={false}
                  size="small"
                  rowKey="id"
                  scroll={{ x: 1000 }}
                  locale={{
                    emptyText: (
                      <Empty
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                        description={
                          <span
                            style={{ color: '#64748b', fontFamily: "'Be Vietnam Pro', sans-serif" }}
                          >
                            Không có dữ liệu
                          </span>
                        }
                      />
                    ),
                  }}
                  style={{
                    fontFamily: "'Be Vietnam Pro', sans-serif",
                  }}
                />
              </StyledTable>
            ) : (
              <Empty description="Không có dữ liệu" />
            )}
          </CardContent>
        </Card>
      </Spin>
    </PageContainer>
  );
};

export default Dashboard;
